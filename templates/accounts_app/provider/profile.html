{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Business Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Service Provider Profile */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Profile Section */
    .profile-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .profile-container {
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Profile Header */
    .profile-hero {
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        padding: 3rem;
        margin-bottom: 3rem;
        box-shadow: var(--cw-shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .profile-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="profile-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23profile-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .profile-hero .content {
        position: relative;
        z-index: 2;
    }

    .business-logo-container {
        position: relative;
        display: inline-block;
        margin-bottom: 1.5rem;
    }

    .business-logo {
        width: 140px;
        height: 140px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: var(--cw-shadow-lg);
        transition: all 0.3s ease;
    }

    .business-name {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .business-email {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 1rem;
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-family: var(--cw-font-heading);
        font-size: 0.875rem;
        font-weight: 600;
        letter-spacing: 0.025em;
        margin-bottom: 1.5rem;
    }

    .status-badge.visible {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .status-badge.hidden {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        color: white;
    }

    /* Section Titles */
    .section-title {
        font-family: var(--cw-font-heading);
        font-size: 2rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        position: relative;
        padding-bottom: 0.5rem;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: var(--cw-gradient-brand-button);
        border-radius: 2px;
    }

    /* Profile Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        margin-bottom: 2rem;
    }

    .card-cw:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .card-header-cw {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .card-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-grow: 1;
    }

    .card-title-cw i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    .card-body-cw {
        padding: 2rem;
    }

    /* Profile Info Rows */
    .profile-info-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .profile-info-row:last-child {
        border-bottom: none;
    }

    .profile-info-row:hover {
        background: var(--cw-accent-light);
        margin: 0 -2rem;
        padding-left: 2rem;
        padding-right: 2rem;
        border-radius: 0.5rem;
    }

    .profile-info-label {
        font-weight: 600;
        color: var(--cw-neutral-700);
        font-family: var(--cw-font-heading);
        min-width: 140px;
        flex-shrink: 0;
    }

    .profile-info-value {
        color: var(--cw-neutral-800);
        font-weight: 500;
        text-align: right;
        flex-grow: 1;
    }

    .profile-info-value.empty {
        color: var(--cw-neutral-600);
        font-style: italic;
    }

    .profile-info-value a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .profile-info-value a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Team Member Cards */
    .team-member-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
        box-shadow: var(--cw-shadow-sm);
    }

    .team-member-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .team-member-photo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--cw-brand-accent);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .team-member-card:hover .team-member-photo {
        border-color: var(--cw-brand-primary);
    }

    .team-member-name {
        font-family: var(--cw-font-heading);
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .team-member-position {
        color: var(--cw-neutral-600);
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .team-status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .team-status-badge.active {
        background: #059669;
        color: white;
    }

    .team-status-badge.inactive {
        background: var(--cw-neutral-600);
        color: white;
    }

    .team-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .empty-team-state {
        text-align: center;
        padding: 3rem 2rem;
        background: var(--cw-accent-light);
        border-radius: 1rem;
        border: 2px dashed var(--cw-brand-accent);
    }

    .empty-team-state i {
        color: var(--cw-neutral-600);
        margin-bottom: 1rem;
    }

    .empty-team-state h6 {
        color: var(--cw-neutral-700);
        margin-bottom: 0.5rem;
    }

    .empty-team-state p {
        color: var(--cw-neutral-600);
        margin-bottom: 1.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-accent {
        background: var(--cw-brand-accent);
        border: 2px solid var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .btn-cw-accent:hover {
        background: var(--cw-accent-dark);
        border-color: var(--cw-accent-dark);
        color: var(--cw-brand-primary);
        transform: translateY(-1px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-sm);
    }

    /* Profile Actions Section */
    .profile-actions-section {
        background: var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 3rem;
        margin-top: 3rem;
        position: relative;
        overflow: hidden;
    }

    .profile-actions-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="actions-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,10 Q25,15 20,20 Q15,15 20,10" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23actions-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .profile-actions-section .content {
        position: relative;
        z-index: 2;
    }

    .actions-title {
        font-family: var(--cw-font-display);
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 2rem;
        text-align: center;
    }

    .profile-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        justify-content: center;
    }

    /* Social Media Links */
    .social-links {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .social-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .social-link:hover {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .profile-section {
            padding: 3rem 0;
        }

        .profile-container {
            padding: 0 1.5rem;
        }

        .profile-hero {
            padding: 2rem;
            text-align: center;
        }

        .business-name {
            font-size: 2rem;
        }

        .section-title {
            font-size: 1.75rem;
        }

        .actions-title {
            font-size: 1.875rem;
        }

        .profile-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary,
        .btn-cw-danger {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .profile-info-row {
            flex-direction: column;
            gap: 0.5rem;
        }

        .profile-info-label {
            min-width: auto;
        }

        .profile-info-value {
            text-align: left;
        }

        .card-header-cw {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .social-links {
            flex-direction: column;
        }
    }

    @media (max-width: 576px) {
        .profile-container {
            padding: 0 1rem;
        }

        .profile-hero {
            padding: 1.5rem;
        }

        .profile-actions-section {
            padding: 2rem;
        }

        .card-body-cw {
            padding: 1.5rem;
        }

        .business-logo {
            width: 120px;
            height: 120px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
/**
 * Service Provider Profile Page JavaScript
 * Handles account deactivation confirmation and animations
 */
document.addEventListener('DOMContentLoaded', function() {
    // Account deactivation confirmation
    const deactivateBtn = document.getElementById('deactivate-account-btn');
    if (deactivateBtn) {
        deactivateBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const confirmMessage =
                'Are you sure you want to deactivate your business account?\n\n' +
                '• Your profile will be hidden from customers\n' +
                '• Your booking history will be preserved\n' +
                '• You can reactivate your account anytime by logging in\n\n' +
                'Click OK to proceed or Cancel to keep your account active.';

            if (confirm(confirmMessage)) {
                // Add loading state to button
                this.innerHTML = '<i class="fas fa-spinner fa-spin" aria-hidden="true"></i> Deactivating...';
                this.disabled = true;

                setTimeout(() => {
                    document.getElementById('deactivate-form').submit();
                }, 1000);
            }
        });
    }

    // Team member action confirmations
    document.querySelectorAll('.team-delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const memberName = this.dataset.memberName;

            if (confirm(`Are you sure you want to remove ${memberName} from your team? This action cannot be undone.`)) {
                this.submit();
            }
        });
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Apply fade-in animation to all profile cards
    document.querySelectorAll('.profile-fade-in').forEach(element => {
        observer.observe(element);
    });
});
</script>
{% endblock %}

{% block content %}
<section class="profile-section">
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-hero">
            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center mb-3 mb-md-0">
                        <div class="business-logo-container">
                            {% if profile.logo %}
                                <img src="{{ profile.logo.url }}" alt="Business Logo" class="business-logo">
                            {% else %}
                                <img src="https://via.placeholder.com/140x140/fae1d7/2F160F?text=No+Logo" alt="Business Logo" class="business-logo">
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-9">
                        <h1 class="business-name">{{ profile.business_name|default:"Business Name" }}</h1>
                        <p class="business-email">{{ profile.user.email }}</p>

                        <!-- Status badge -->
                        {% if profile.is_public %}
                            <div class="status-badge visible">
                                <i class="fas fa-eye" aria-hidden="true"></i>
                                Visible to Customers
                            </div>
                        {% else %}
                            <div class="status-badge hidden">
                                <i class="fas fa-eye-slash" aria-hidden="true"></i>
                                Hidden from Customers
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Location & Contact Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                                Location & Contact
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-info-row">
                                <div class="profile-info-label">Address:</div>
                                <div class="profile-info-value {% if not profile.get_full_address %}empty{% endif %}">
                                    {{ profile.get_full_address|default:"Address not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Website:</div>
                                <div class="profile-info-value {% if not profile.website %}empty{% endif %}">
                                    {% if profile.website %}
                                        <a href="{{ profile.website }}" target="_blank" rel="noopener noreferrer">
                                            {{ profile.website }} <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    {% else %}
                                        Not provided
                                    {% endif %}
                                </div>
                            </div>
                            {% if profile.instagram or profile.facebook %}
                            <div class="profile-info-row">
                                <div class="profile-info-label">Social Media:</div>
                                <div class="profile-info-value">
                                    <div class="social-links">
                                        {% if profile.instagram %}
                                            <a href="{{ profile.instagram }}" target="_blank" rel="noopener noreferrer" class="social-link">
                                                <i class="fab fa-instagram"></i>
                                                Instagram
                                            </a>
                                        {% endif %}
                                        {% if profile.facebook %}
                                            <a href="{{ profile.facebook }}" target="_blank" rel="noopener noreferrer" class="social-link">
                                                <i class="fab fa-facebook"></i>
                                                Facebook
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Description -->
            {% if profile.description %}
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Business Description
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <p class="mb-0">{{ profile.description }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Team Management Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Team Management</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-users" aria-hidden="true"></i>
                                Team Members ({{ team_count }}/{{ max_team_members }})
                            </h3>
                            {% if team_count < max_team_members %}
                                <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                                    <i class="fas fa-plus" aria-hidden="true"></i>
                                    Add Member
                                </a>
                            {% endif %}
                        </div>
                        <div class="card-body-cw">
                            {% if team_members %}
                                <div class="row">
                                    {% for member in team_members %}
                                        <div class="col-lg-4 col-md-6 mb-3">
                                            <div class="team-member-card">
                                                {% if member.photo %}
                                                    <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="team-member-photo">
                                                {% else %}
                                                    <div class="team-member-photo d-flex align-items-center justify-content-center" style="background: var(--cw-accent-light);">
                                                        <i class="fas fa-user fa-2x" style="color: var(--cw-neutral-600);"></i>
                                                    </div>
                                                {% endif %}
                                                <h6 class="team-member-name">{{ member.name }}</h6>
                                                <p class="team-member-position">{{ member.position }}</p>

                                                <!-- Status badge -->
                                                <div class="team-status-badge {% if member.is_active %}active{% else %}inactive{% endif %}">
                                                    <i class="fas fa-{% if member.is_active %}check-circle{% else %}pause-circle{% endif %}"></i>
                                                    {% if member.is_active %}Active{% else %}Inactive{% endif %}
                                                </div>

                                                <!-- Action buttons -->
                                                <div class="team-actions">
                                                    <a href="{% url 'accounts_app:team_member_edit' member.id %}" class="btn-cw-accent">
                                                        <i class="fas fa-edit"></i>
                                                        Edit
                                                    </a>
                                                    <form method="post" action="{% url 'accounts_app:team_member_toggle_status' member.id %}" class="d-inline">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-cw-secondary">
                                                            <i class="fas fa-{% if member.is_active %}eye-slash{% else %}eye{% endif %}"></i>
                                                            {% if member.is_active %}Deactivate{% else %}Activate{% endif %}
                                                        </button>
                                                    </form>
                                                    <form method="post" action="{% url 'accounts_app:team_member_delete' member.id %}" class="team-delete-form" data-member-name="{{ member.name }}">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-cw-danger">
                                                            <i class="fas fa-trash"></i>
                                                            Remove
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="empty-team-state">
                                    <i class="fas fa-users fa-3x"></i>
                                    <h6>No team members added yet</h6>
                                    <p>Add team members to showcase your staff and their expertise to potential customers.</p>
                                    <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                                        <i class="fas fa-plus"></i>
                                        Add Your First Team Member
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Account Information</h2>
            <div class="row">
                <div class="col-12">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                Account Details
                            </h3>
                        </div>
                        <div class="card-body-cw">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Email Address:</div>
                                        <div class="profile-info-value">{{ profile.user.email }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Account Type:</div>
                                        <div class="profile-info-value">
                                            <span class="team-status-badge active">Service Provider</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Member Since:</div>
                                        <div class="profile-info-value">{{ profile.user.date_joined|date:"F d, Y" }}</div>
                                    </div>
                                    <div class="profile-info-row">
                                        <div class="profile-info-label">Last Login:</div>
                                        <div class="profile-info-value">
                                            {{ profile.user.last_login|date:"F d, Y g:i A"|default:"Never" }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Actions Section -->
        <div class="profile-actions-section">
            <div class="content">
                <h2 class="actions-title">Account Management</h2>
                <div class="profile-actions">
                    <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="btn-cw-primary">
                        <i class="fas fa-edit" aria-hidden="true"></i>
                        Edit Profile
                    </a>
                    <a href="{% url 'accounts_app:service_provider_change_password' %}" class="btn-cw-secondary">
                        <i class="fas fa-key" aria-hidden="true"></i>
                        Change Password
                    </a>
                    <button type="button" id="deactivate-account-btn" class="btn-cw-danger">
                        <i class="fas fa-user-times" aria-hidden="true"></i>
                        Deactivate Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hidden deactivate form -->
<form id="deactivate-form" method="post" action="{% url 'accounts_app:service_provider_deactivate' %}" style="display: none;">
    {% csrf_token %}
</form>
{% endblock %}
        </div>

        <!-- Business Information Section -->
        <div class="profile-info-section">
            <h2 class="section-title">Business Information</h2>
            <div class="row">
                <!-- Business Details Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card-cw">
                        <div class="card-header-cw">
                            <h3 class="card-title-cw">
                                <i class="fas fa-building" aria-hidden="true"></i>
                                Business Details
                            </h3>
                            <a href="{% url 'accounts_app:service_provider_profile_edit' %}" class="btn-cw-accent">
                                <i class="fas fa-edit" aria-hidden="true"></i>
                                Edit
                            </a>
                        </div>
                        <div class="card-body-cw">
                            <div class="profile-info-row">
                                <div class="profile-info-label">Legal Name:</div>
                                <div class="profile-info-value {% if not profile.legal_name %}empty{% endif %}">
                                    {{ profile.legal_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">DBA Name:</div>
                                <div class="profile-info-value {% if not profile.display_name %}empty{% endif %}">
                                    {{ profile.display_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Phone:</div>
                                <div class="profile-info-value {% if not profile.phone %}empty{% endif %}">
                                    {{ profile.phone|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">Contact Person:</div>
                                <div class="profile-info-value {% if not profile.contact_name %}empty{% endif %}">
                                    {{ profile.contact_name|default:"Not provided" }}
                                </div>
                            </div>
                            <div class="profile-info-row">
                                <div class="profile-info-label">EIN:</div>
                                <div class="profile-info-value {% if not profile.ein %}empty{% endif %}">
                                    {{ profile.ein|default:"Not provided" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
